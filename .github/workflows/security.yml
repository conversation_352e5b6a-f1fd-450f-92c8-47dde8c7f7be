name: Security Testing

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Shallow clones should be disabled for better analysis
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt
    
    - name: Run Bandit Security Scan
      working-directory: ./backend
      run: |
        mkdir -p reports/security
        bandit -r app/ -f json -o reports/security/bandit-results.json || true
        bandit -r app/ -f txt -o reports/security/bandit-report.txt || true
    
    - name: Run Safety Dependency Check
      working-directory: ./backend
      run: |
        safety check --json --output reports/security/safety-results.json || true
        safety check
    
    - name: Run Semgrep Security Analysis
      working-directory: ./backend
      run: |
        pip install semgrep
        semgrep --config=auto --json --output reports/security/semgrep-results.json app/ || true
    
    - name: Run Secrets Scan
      working-directory: ./backend
      run: |
        python scripts/security_scan.py --scan secrets
    
    - name: Run Security Tests
      working-directory: ./backend
      run: |
        pytest tests/security/ -v --tb=short --junitxml=reports/security/security-tests.xml
    
    - name: Upload Security Reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: backend/reports/security/
        retention-days: 30
    
    - name: Comment PR with Security Results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          
          try {
            const banditResults = JSON.parse(fs.readFileSync('backend/reports/security/bandit-results.json', 'utf8'));
            const safetyResults = JSON.parse(fs.readFileSync('backend/reports/security/safety-results.json', 'utf8'));
            
            let comment = '## 🔒 Security Scan Results\n\n';
            
            // Bandit results
            const banditIssues = banditResults.results || [];
            const highSeverity = banditIssues.filter(issue => issue.issue_severity === 'HIGH').length;
            const mediumSeverity = banditIssues.filter(issue => issue.issue_severity === 'MEDIUM').length;
            
            comment += `### Static Analysis (Bandit)\n`;
            comment += `- 🔴 High Severity: ${highSeverity}\n`;
            comment += `- 🟡 Medium Severity: ${mediumSeverity}\n`;
            comment += `- Total Issues: ${banditIssues.length}\n\n`;
            
            // Safety results
            const vulnerabilities = safetyResults.vulnerabilities || [];
            comment += `### Dependency Vulnerabilities (Safety)\n`;
            comment += `- 🚨 Vulnerable Dependencies: ${vulnerabilities.length}\n\n`;
            
            if (highSeverity > 0 || vulnerabilities.length > 0) {
              comment += '⚠️ **Security issues found!** Please review the security reports.\n\n';
            } else {
              comment += '✅ **No critical security issues found!**\n\n';
            }
            
            comment += 'Full reports are available in the workflow artifacts.';
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          } catch (error) {
            console.log('Could not parse security results:', error);
          }

  docker-security:
    runs-on: ubuntu-latest
    if: github.event_name != 'schedule'  # Skip on scheduled runs
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build Docker image
      working-directory: ./backend
      run: |
        docker build -t blast-radius-backend:test .
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'blast-radius-backend:test'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  codeql-analysis:
    name: CodeQL Analysis
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    
    strategy:
      fail-fast: false
      matrix:
        language: [ 'python', 'javascript' ]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: ${{ matrix.language }}
        queries: security-extended,security-and-quality
    
    - name: Autobuild
      uses: github/codeql-action/autobuild@v2
    
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2
      with:
        category: "/language:${{matrix.language}}"

  security-policy-check:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Check for Security Policy
      run: |
        if [ ! -f SECURITY.md ]; then
          echo "⚠️ SECURITY.md file not found. Creating template..."
          cat > SECURITY.md << 'EOF'
        # Security Policy
        
        ## Supported Versions
        
        | Version | Supported          |
        | ------- | ------------------ |
        | 1.0.x   | :white_check_mark: |
        | < 1.0   | :x:                |
        
        ## Reporting a Vulnerability
        
        Please report security <NAME_EMAIL>
        
        We will respond within 48 hours and provide updates every 7 days.
        EOF
          echo "📝 Created SECURITY.md template"
        else
          echo "✅ SECURITY.md file exists"
        fi
    
    - name: Check for Dependabot Configuration
      run: |
        if [ ! -f .github/dependabot.yml ]; then
          echo "⚠️ Dependabot configuration not found. Creating template..."
          mkdir -p .github
          cat > .github/dependabot.yml << 'EOF'
        version: 2
        updates:
          - package-ecosystem: "pip"
            directory: "/backend"
            schedule:
              interval: "weekly"
            open-pull-requests-limit: 10
            reviewers:
              - "security-team"
            labels:
              - "dependencies"
              - "security"
          
          - package-ecosystem: "npm"
            directory: "/frontend"
            schedule:
              interval: "weekly"
            open-pull-requests-limit: 10
            reviewers:
              - "security-team"
            labels:
              - "dependencies"
              - "security"
        
          - package-ecosystem: "docker"
            directory: "/"
            schedule:
              interval: "weekly"
            reviewers:
              - "security-team"
            labels:
              - "dependencies"
              - "security"
        EOF
          echo "📝 Created Dependabot configuration"
        else
          echo "✅ Dependabot configuration exists"
        fi

  security-compliance:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Check Security Headers Configuration
      working-directory: ./backend
      run: |
        echo "🔍 Checking security headers configuration..."
        if grep -r "X-Content-Type-Options" app/; then
          echo "✅ X-Content-Type-Options header configured"
        else
          echo "❌ X-Content-Type-Options header not found"
          exit 1
        fi
        
        if grep -r "X-Frame-Options" app/; then
          echo "✅ X-Frame-Options header configured"
        else
          echo "❌ X-Frame-Options header not found"
          exit 1
        fi
    
    - name: Check Authentication Implementation
      working-directory: ./backend
      run: |
        echo "🔍 Checking authentication implementation..."
        if grep -r "JWT" app/ && grep -r "password" app/; then
          echo "✅ Authentication mechanisms found"
        else
          echo "❌ Authentication implementation incomplete"
          exit 1
        fi
    
    - name: Check Logging Configuration
      working-directory: ./backend
      run: |
        echo "🔍 Checking logging configuration..."
        if grep -r "logging" app/ && grep -r "audit" app/; then
          echo "✅ Logging and audit trail configured"
        else
          echo "❌ Logging configuration incomplete"
          exit 1
        fi
