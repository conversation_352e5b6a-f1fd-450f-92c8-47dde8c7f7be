# Test environment configuration
SECRET_KEY=test-secret-key-for-testing-only-not-for-production
DATABASE_URL=sqlite:///./test.db
REDIS_URL=redis://localhost:6379/1
NEO4J_URL=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=test_password

# Test-specific settings
TESTING=true
DEBUG=true
LOG_LEVEL=INFO
ENVIRONMENT=test

# Security settings for testing
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
PASSWORD_RESET_TOKEN_EXPIRE_HOURS=24

# Database settings
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=10
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# Session settings
SESSION_TIMEOUT_MINUTES=60
MAX_CONCURRENT_SESSIONS_PER_USER=5

# Rate limiting
RATE_LIMIT_ENABLED=false
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# CORS settings
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
CORS_ALLOW_HEADERS=["*"]

# Monitoring
PROMETHEUS_ENABLED=false
AUDIT_LOG_ENABLED=true

# Email settings (disabled for testing)
EMAIL_ENABLED=false
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USER=test
SMTP_PASSWORD=test
EMAIL_FROM=<EMAIL>

# Azure AD settings (disabled for testing)
AZURE_AD_ENABLED=false
AZURE_AD_TENANT_ID=test
AZURE_AD_CLIENT_ID=test
AZURE_AD_CLIENT_SECRET=test
