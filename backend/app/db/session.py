"""Database session management."""

from collections.abc import Generator
from datetime import datetime, timedelta
import logging
import uuid

from sqlalchemy import create_engine, event
from sqlalchemy.orm import Session, sessionmaker

from app.config import get_database_url, settings
from app.db.base import AuditLog

logger = logging.getLogger(__name__)

# Create database engine
engine = create_engine(
    get_database_url(),
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_timeout=settings.DATABASE_POOL_TIMEOUT,
    pool_recycle=settings.DATABASE_POOL_RECYCLE,
    pool_pre_ping=True,
    echo=settings.DEBUG,
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[Session, None, None]:
    """
    Get database session.

    Yields:
        Database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


class AuditContext:
    """Context for audit logging."""

    def __init__(self):
        self.user_id: uuid.UUID | None = None
        self.user_ip: str | None = None
        self.user_agent: str | None = None
        self.session_id: str | None = None
        self.request_id: str | None = None
        self.api_endpoint: str | None = None
        self.http_method: str | None = None


# Global audit context
audit_context = AuditContext()


def set_audit_context(
    user_id: uuid.UUID | None = None,
    user_ip: str | None = None,
    user_agent: str | None = None,
    session_id: str | None = None,
    request_id: str | None = None,
    api_endpoint: str | None = None,
    http_method: str | None = None,
) -> None:
    """
    Set audit context for current request.

    Args:
        user_id: ID of the user making the request
        user_ip: IP address of the user
        user_agent: User agent string
        session_id: Session ID
        request_id: Request ID for tracing
        api_endpoint: API endpoint being accessed
        http_method: HTTP method used
    """
    audit_context.user_id = user_id
    audit_context.user_ip = user_ip
    audit_context.user_agent = user_agent
    audit_context.session_id = session_id
    audit_context.request_id = request_id
    audit_context.api_endpoint = api_endpoint
    audit_context.http_method = http_method


def clear_audit_context() -> None:
    """Clear audit context."""
    audit_context.user_id = None
    audit_context.user_ip = None
    audit_context.user_agent = None
    audit_context.session_id = None
    audit_context.request_id = None
    audit_context.api_endpoint = None
    audit_context.http_method = None


def create_audit_log(
    session: Session,
    table_name: str,
    record_id: uuid.UUID,
    action: str,
    old_values: dict | None = None,
    new_values: dict | None = None,
) -> None:
    """
    Create an audit log entry.

    Args:
        session: Database session
        table_name: Name of the table being modified
        record_id: ID of the record being modified
        action: Action being performed (CREATE, UPDATE, DELETE, RESTORE)
        old_values: Previous values (for UPDATE and DELETE)
        new_values: New values (for CREATE and UPDATE)
    """
    if not settings.AUDIT_LOG_ENABLED:
        return

    try:
        audit_log = AuditLog(
            table_name=table_name,
            record_id=record_id,
            action=action,
            old_values=old_values,
            new_values=new_values,
            user_id=audit_context.user_id,
            user_ip=audit_context.user_ip,
            user_agent=audit_context.user_agent,
            session_id=audit_context.session_id,
            request_id=audit_context.request_id,
            api_endpoint=audit_context.api_endpoint,
            http_method=audit_context.http_method,
        )
        session.add(audit_log)
        session.flush()  # Don't commit here, let the main transaction handle it
    except Exception as e:
        logger.error(f"Failed to create audit log: {e}")


# SQLAlchemy event listeners for automatic audit logging
@event.listens_for(SessionLocal, "before_insert")
def before_insert_listener(mapper, connection, target):
    """Listen for insert events to create audit logs."""
    if isinstance(target, AuditLog):
        return  # Don't audit the audit logs themselves

    # Store the new values for the audit log
    if hasattr(target, "_audit_new_values"):
        target._audit_new_values = target.to_dict(exclude_deleted=False)


@event.listens_for(SessionLocal, "after_insert")
def after_insert_listener(mapper, connection, target):
    """Create audit log after insert."""
    if isinstance(target, AuditLog):
        return

    if settings.AUDIT_LOG_ENABLED:
        try:
            # Create a new session for audit logging to avoid conflicts
            audit_session = SessionLocal()
            try:
                new_values = target.to_dict(exclude_deleted=False)
                create_audit_log(
                    audit_session,
                    target.__tablename__,
                    target.id,
                    "CREATE",
                    old_values=None,
                    new_values=new_values,
                )
                audit_session.commit()
            finally:
                audit_session.close()
        except Exception as e:
            logger.error(f"Failed to create insert audit log: {e}")


@event.listens_for(SessionLocal, "before_update")
def before_update_listener(mapper, connection, target):
    """Listen for update events to capture old values."""
    if isinstance(target, AuditLog):
        return

    # Store the old values before update
    if hasattr(target, "to_dict"):
        target._audit_old_values = target.to_dict(exclude_deleted=False)


@event.listens_for(SessionLocal, "after_update")
def after_update_listener(mapper, connection, target):
    """Create audit log after update."""
    if isinstance(target, AuditLog):
        return

    if settings.AUDIT_LOG_ENABLED:
        try:
            audit_session = SessionLocal()
            try:
                old_values = getattr(target, "_audit_old_values", None)
                new_values = target.to_dict(exclude_deleted=False)

                # Determine if this is a soft delete or restore
                action = "UPDATE"
                if old_values and new_values:
                    if not old_values.get("is_deleted") and new_values.get(
                        "is_deleted"
                    ):
                        action = "DELETE"
                    elif old_values.get("is_deleted") and not new_values.get(
                        "is_deleted"
                    ):
                        action = "RESTORE"

                create_audit_log(
                    audit_session,
                    target.__tablename__,
                    target.id,
                    action,
                    old_values=old_values,
                    new_values=new_values,
                )
                audit_session.commit()
            finally:
                audit_session.close()
        except Exception as e:
            logger.error(f"Failed to create update audit log: {e}")


class DatabaseManager:
    """Database management utilities."""

    @staticmethod
    def create_all_tables():
        """Create all database tables."""
        from app.db.base import Base

        Base.metadata.create_all(bind=engine)

    @staticmethod
    def drop_all_tables():
        """Drop all database tables."""
        from app.db.base import Base

        Base.metadata.drop_all(bind=engine)

    @staticmethod
    def get_table_info():
        """Get information about database tables."""
        from app.db.base import Base

        tables = []
        for table_name, table in Base.metadata.tables.items():
            tables.append(
                {
                    "name": table_name,
                    "columns": [col.name for col in table.columns],
                    "indexes": [idx.name for idx in table.indexes],
                }
            )
        return tables

    @staticmethod
    def cleanup_audit_logs(days_to_keep: int = 365):
        """
        Clean up old audit logs.

        Args:
            days_to_keep: Number of days to keep audit logs
        """
        if not settings.AUDIT_LOG_ENABLED:
            return

        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)

        with SessionLocal() as session:
            deleted_count = (
                session.query(AuditLog)
                .filter(AuditLog.created_at < cutoff_date)
                .delete()
            )
            session.commit()
            logger.info(f"Cleaned up {deleted_count} old audit log entries")

    @staticmethod
    def get_database_stats():
        """Get database statistics."""
        with SessionLocal() as session:
            stats = {}

            # Get table row counts
            from app.db.base import Base

            for table_name, table in Base.metadata.tables.items():
                try:
                    count = session.execute(
                        f"SELECT COUNT(*) FROM {table_name}"
                    ).scalar()
                    stats[table_name] = count
                except Exception as e:
                    logger.error(f"Failed to get count for table {table_name}: {e}")
                    stats[table_name] = -1

            return stats


# Export commonly used items
__all__ = [
    "DatabaseManager",
    "SessionLocal",
    "clear_audit_context",
    "create_audit_log",
    "engine",
    "get_db",
    "set_audit_context",
]
