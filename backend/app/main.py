"""Main FastAPI application for Blast-Radius Security Tool.

This module contains the main FastAPI application instance with all middleware,
routers, and configuration for the Blast-Radius Security Tool API.

The application provides:
- Authentication and authorization endpoints
- User management functionality
- Security middleware for headers and audit logging
- Health check and metrics endpoints
- Comprehensive error handling

Example:
    Run the application directly:
        $ python -m app.main

    Or use uvicorn:
        $ uvicorn app.main:app --reload

Attributes:
    app (FastAPI): The main FastAPI application instance.
    logger (logging.Logger): Application logger instance.
"""

from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager
import logging
import time

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

from app.api.v1.auth import router as auth_router
from app.api.v1.users import router as users_router
from app.config import settings
from app.core.security import SecurityHeaders
from app.db.session import clear_audit_context

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers to all responses.

    This middleware automatically adds security headers to all HTTP responses
    to improve the security posture of the application.

    Headers added include:
    - X-Content-Type-Options: nosniff
    - X-Frame-Options: DENY
    - X-XSS-Protection: 1; mode=block
    - Strict-Transport-Security: max-age=31536000; includeSubDomains
    - Content-Security-Policy: default-src 'self'
    """

    async def dispatch(self, request: Request, call_next) -> Response:
        """Add security headers to response.

        Args:
            request: The incoming HTTP request.
            call_next: The next middleware or route handler.

        Returns:
            Response: The HTTP response with security headers added.
        """
        response = await call_next(request)

        # Add security headers
        security_headers = SecurityHeaders.get_security_headers()
        for header, value in security_headers.items():
            response.headers[header] = value

        return response


class AuditContextMiddleware(BaseHTTPMiddleware):
    """Middleware to manage audit context for requests.

    This middleware ensures that audit context is properly managed
    throughout the request lifecycle and cleaned up after each request.
    """

    async def dispatch(self, request: Request, call_next) -> Response:
        """Manage audit context for the request.

        Args:
            request: The incoming HTTP request.
            call_next: The next middleware or route handler.

        Returns:
            Response: The HTTP response.
        """
        try:
            response = await call_next(request)
            return response
        finally:
            # Clear audit context after request
            clear_audit_context()


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware to log all requests and responses.

    This middleware logs detailed information about incoming requests
    and outgoing responses, including timing information for performance
    monitoring.
    """

    async def dispatch(self, request: Request, call_next) -> Response:
        """Log request details and response timing.

        Args:
            request: The incoming HTTP request.
            call_next: The next middleware or route handler.

        Returns:
            Response: The HTTP response with timing header added.
        """
        start_time = time.time()

        # Log request
        client_ip = request.client.host if request.client else "unknown"
        logger.info(
            "Request: %s %s from %s User-Agent: %s",
            request.method,
            request.url.path,
            client_ip,
            request.headers.get("user-agent", "unknown"),
        )

        response = await call_next(request)

        # Log response
        process_time = time.time() - start_time
        logger.info(
            "Response: %s for %s %s in %.4fs",
            response.status_code,
            request.method,
            request.url.path,
            process_time,
        )

        # Add timing header
        response.headers["X-Process-Time"] = str(process_time)

        return response


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan events.

    Manages application startup and shutdown events including:
    - Database table creation and verification
    - System role initialization
    - Resource cleanup on shutdown

    Args:
        app: The FastAPI application instance.

    Yields:
        None: Control to the application during its lifetime.

    Raises:
        Exception: If startup initialization fails.
    """
    # Startup
    logger.info("Starting Blast-Radius Security Tool API")

    # Initialize database tables and system roles
    try:
        from app.db.base import Base
        from app.db.models.user import UserRole
        from app.db.session import SessionLocal, engine

        # Create tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created/verified")

        # Create system roles
        with SessionLocal() as db:
            UserRole.create_system_roles(db)
        logger.info("System roles created/verified")

    except Exception as e:
        logger.error("Startup error: %s", e)
        raise

    yield

    # Shutdown
    logger.info("Shutting down Blast-Radius Security Tool API")


# Create FastAPI application
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="Comprehensive security platform for attack path analysis and threat intelligence",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    openapi_url="/openapi.json" if settings.DEBUG else None,
    lifespan=lifespan,
)

# Add middleware
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(AuditContextMiddleware)

if settings.DEBUG:
    app.add_middleware(RequestLoggingMiddleware)

# CORS middleware
if settings.CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.CORS_ORIGINS],
        allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
        allow_methods=settings.CORS_ALLOW_METHODS,
        allow_headers=settings.CORS_ALLOW_HEADERS,
    )

# Trusted host middleware for production
if not settings.DEBUG:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"],  # Configure appropriately for production
    )

# Include routers
app.include_router(
    auth_router, prefix=f"{settings.API_V1_STR}/auth", tags=["authentication"]
)

app.include_router(users_router, prefix=f"{settings.API_V1_STR}/users", tags=["users"])


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Blast-Radius Security Tool API",
        "version": "1.0.0",
        "status": "operational",
        "docs": "/docs" if settings.DEBUG else "disabled",
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check database connectivity
        from app.db.session import SessionLocal

        with SessionLocal() as db:
            db.execute("SELECT 1")

        return {
            "status": "healthy",
            "timestamp": time.time(),
            "database": "connected",
            "version": "1.0.0",
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "timestamp": time.time(),
                "database": "disconnected",
                "error": str(e),
            },
        )


@app.get("/metrics")
async def metrics():
    """Metrics endpoint for monitoring."""
    if not settings.PROMETHEUS_ENABLED:
        return JSONResponse(
            status_code=404, content={"detail": "Metrics endpoint disabled"}
        )

    try:
        from app.db.session import SessionLocal
        from app.services.user_service import UserService

        with SessionLocal() as db:
            user_service = UserService(db)
            stats = user_service.get_user_statistics()

        return {
            "timestamp": time.time(),
            "user_stats": stats,
            "system_info": {
                "debug_mode": settings.DEBUG,
                "environment": settings.ENVIRONMENT,
            },
        }
    except Exception as e:
        logger.error(f"Metrics error: {e}")
        return JSONResponse(status_code=500, content={"detail": "Metrics unavailable"})


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)

    if settings.DEBUG:
        return JSONResponse(
            status_code=500,
            content={
                "detail": "Internal server error",
                "error": str(exc),
                "type": type(exc).__name__,
            },
        )
    return JSONResponse(status_code=500, content={"detail": "Internal server error"})


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
    )
