"""Pydantic schemas for API request/response models."""

from .auth import (
    LoginRequest,
    LoginResponse,
    MFABackupCodesResponse,
    MFASetupRequest,
    MFAVerifyRequest,
    PasswordResetConfirm,
    PasswordResetRequest,
    RefreshTokenRequest,
    TokenResponse,
)
from .user import (
    UserAPIKeyCreate,
    UserAPIKeyResponse,
    UserCreate,
    UserListResponse,
    UserResponse,
    UserRoleAssignment,
    UserSessionResponse,
    UserUpdate,
)

__all__ = [
    # Authentication schemas
    "LoginRequest",
    "LoginResponse",
    "TokenResponse",
    "RefreshTokenRequest",
    "PasswordResetRequest",
    "PasswordResetConfirm",
    "MFASetupRequest",
    "MFAVerifyRequest",
    "MFABackupCodesResponse",
    # User schemas
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserListResponse",
    "UserRoleAssignment",
    "UserSessionResponse",
    "UserAPIKeyCreate",
    "UserAPIKeyResponse",
]
