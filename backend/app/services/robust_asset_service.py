"""Robust asset service with soft-delete, audit trails, and enterprise features."""

import hashlib
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from sqlalchemy import and_, or_, desc, asc, func
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import IntegrityError

from app.db.models.asset_robust import (
    RobustAsset,
    AssetAuditLog,
    AssetVersion,
    AssetLock,
    AssetValidation,
    AssetBackup,
    AuditAction,
    DataRetentionPolicy,
)

logger = logging.getLogger(__name__)


class AssetLockError(Exception):
    """Raised when asset is locked and cannot be modified."""
    pass


class AssetValidationError(Exception):
    """Raised when asset validation fails."""
    pass


class AssetNotFoundError(Exception):
    """Raised when asset is not found."""
    pass


class RobustAssetService:
    """Enterprise-grade asset service with comprehensive data management."""
    
    def __init__(self, db: Session, user_id: str = None, session_id: str = None):
        self.db = db
        self.user_id = user_id
        self.session_id = session_id
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    # ==================== CORE CRUD OPERATIONS ====================
    
    def create_asset(self, asset_data: Dict[str, Any], 
                    change_reason: str = None,
                    skip_validation: bool = False) -> RobustAsset:
        """Create a new asset with full audit trail."""
        try:
            # Create asset instance
            asset = RobustAsset(**asset_data)
            asset.created_by = self.user_id
            asset.updated_by = self.user_id
            
            # Calculate checksum
            asset.checksum = self._calculate_checksum(asset_data)
            
            # Validate asset if required
            if not skip_validation:
                validation_errors = self._validate_asset(asset)
                if validation_errors:
                    raise AssetValidationError(f"Asset validation failed: {validation_errors}")
            
            # Save to database
            self.db.add(asset)
            self.db.flush()  # Get the ID
            
            # Create audit log
            self._create_audit_log(
                asset_id=asset.id,
                action=AuditAction.CREATE,
                table_name="assets_robust",
                record_id=str(asset.id),
                new_value=asset_data,
                change_reason=change_reason,
                after_snapshot=self._create_asset_snapshot(asset)
            )
            
            # Create initial version
            self._create_version(asset, change_reason or "Initial creation", "create")
            
            self.db.commit()
            self.logger.info(f"Created asset: {asset.name} (ID: {asset.id})")
            return asset
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Failed to create asset: {e}")
            raise
    
    def get_asset(self, asset_id: uuid.UUID, 
                 include_deleted: bool = False,
                 include_relationships: bool = False) -> Optional[RobustAsset]:
        """Get asset by ID with optional inclusion of soft-deleted records."""
        query = self.db.query(RobustAsset).filter(RobustAsset.id == asset_id)
        
        if not include_deleted:
            query = query.filter(RobustAsset.is_deleted == False)
        
        if include_relationships:
            query = query.options(
                joinedload(RobustAsset.asset_tags),
                joinedload(RobustAsset.validations)
            )
        
        return query.first()
    
    def update_asset(self, asset_id: uuid.UUID, 
                    update_data: Dict[str, Any],
                    change_reason: str = None,
                    force_update: bool = False) -> RobustAsset:
        """Update asset with comprehensive audit trail and locking."""
        try:
            # Check if asset is locked
            if not force_update and self._is_asset_locked(asset_id, "write"):
                raise AssetLockError(f"Asset {asset_id} is locked for writing")
            
            # Get current asset
            asset = self.get_asset(asset_id)
            if not asset:
                raise AssetNotFoundError(f"Asset {asset_id} not found")
            
            # Create before snapshot
            before_snapshot = self._create_asset_snapshot(asset)
            
            # Store old values for audit
            old_values = {}
            for field, new_value in update_data.items():
                if hasattr(asset, field):
                    old_values[field] = getattr(asset, field)
            
            # Update asset fields
            for field, value in update_data.items():
                if hasattr(asset, field):
                    setattr(asset, field, value)
            
            # Update audit fields
            asset.updated_by = self.user_id
            asset.updated_at = datetime.utcnow()
            asset.version += 1
            asset.change_count += 1
            
            # Recalculate checksum
            asset_dict = self._asset_to_dict(asset)
            asset.checksum = self._calculate_checksum(asset_dict)
            
            # Validate updated asset
            validation_errors = self._validate_asset(asset)
            if validation_errors:
                raise AssetValidationError(f"Asset validation failed: {validation_errors}")
            
            # Create after snapshot
            after_snapshot = self._create_asset_snapshot(asset)
            
            # Create audit logs for each changed field
            for field, old_value in old_values.items():
                new_value = update_data.get(field)
                if old_value != new_value:
                    self._create_audit_log(
                        asset_id=asset.id,
                        action=AuditAction.UPDATE,
                        table_name="assets_robust",
                        record_id=str(asset.id),
                        field_name=field,
                        old_value=old_value,
                        new_value=new_value,
                        change_reason=change_reason,
                        before_snapshot=before_snapshot,
                        after_snapshot=after_snapshot
                    )
            
            # Create version if significant change
            if self._is_significant_change(old_values, update_data):
                self._create_version(asset, change_reason or "Asset update", "update")
            
            self.db.commit()
            self.logger.info(f"Updated asset: {asset.name} (ID: {asset.id})")
            return asset
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Failed to update asset {asset_id}: {e}")
            raise
    
    def soft_delete_asset(self, asset_id: uuid.UUID, 
                         reason: str = None,
                         retention_policy: DataRetentionPolicy = DataRetentionPolicy.LONG_TERM) -> bool:
        """Soft delete asset with audit trail."""
        try:
            asset = self.get_asset(asset_id)
            if not asset:
                raise AssetNotFoundError(f"Asset {asset_id} not found")
            
            if asset.is_deleted:
                self.logger.warning(f"Asset {asset_id} is already deleted")
                return False
            
            # Create before snapshot
            before_snapshot = self._create_asset_snapshot(asset)
            
            # Perform soft delete
            asset.retention_policy = retention_policy
            asset.soft_delete(deleted_by=self.user_id, reason=reason)
            asset.updated_by = self.user_id
            asset.updated_at = datetime.utcnow()
            asset.version += 1
            asset.change_count += 1
            
            # Create audit log
            self._create_audit_log(
                asset_id=asset.id,
                action=AuditAction.SOFT_DELETE,
                table_name="assets_robust",
                record_id=str(asset.id),
                change_reason=reason,
                before_snapshot=before_snapshot,
                after_snapshot=self._create_asset_snapshot(asset)
            )
            
            # Create version
            self._create_version(asset, reason or "Soft delete", "delete")
            
            self.db.commit()
            self.logger.info(f"Soft deleted asset: {asset.name} (ID: {asset.id})")
            return True
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Failed to soft delete asset {asset_id}: {e}")
            raise
    
    def restore_asset(self, asset_id: uuid.UUID, reason: str = None) -> bool:
        """Restore soft-deleted asset."""
        try:
            asset = self.get_asset(asset_id, include_deleted=True)
            if not asset:
                raise AssetNotFoundError(f"Asset {asset_id} not found")
            
            if not asset.is_deleted:
                self.logger.warning(f"Asset {asset_id} is not deleted")
                return False
            
            # Create before snapshot
            before_snapshot = self._create_asset_snapshot(asset)
            
            # Restore asset
            asset.restore(restored_by=self.user_id)
            asset.updated_by = self.user_id
            asset.updated_at = datetime.utcnow()
            asset.version += 1
            asset.change_count += 1
            
            # Create audit log
            self._create_audit_log(
                asset_id=asset.id,
                action=AuditAction.RESTORE,
                table_name="assets_robust",
                record_id=str(asset.id),
                change_reason=reason,
                before_snapshot=before_snapshot,
                after_snapshot=self._create_asset_snapshot(asset)
            )
            
            # Create version
            self._create_version(asset, reason or "Asset restore", "restore")
            
            self.db.commit()
            self.logger.info(f"Restored asset: {asset.name} (ID: {asset.id})")
            return True
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Failed to restore asset {asset_id}: {e}")
            raise
    
    def hard_delete_asset(self, asset_id: uuid.UUID, 
                         confirmation_token: str,
                         reason: str = None) -> bool:
        """Permanently delete asset (requires confirmation)."""
        # Verify confirmation token
        expected_token = hashlib.sha256(f"{asset_id}{self.user_id}".encode()).hexdigest()[:16]
        if confirmation_token != expected_token:
            raise ValueError("Invalid confirmation token")
        
        try:
            asset = self.get_asset(asset_id, include_deleted=True)
            if not asset:
                raise AssetNotFoundError(f"Asset {asset_id} not found")
            
            # Create final audit log
            self._create_audit_log(
                asset_id=asset.id,
                action=AuditAction.DELETE,
                table_name="assets_robust",
                record_id=str(asset.id),
                change_reason=reason,
                before_snapshot=self._create_asset_snapshot(asset)
            )
            
            # Delete related records
            self.db.query(AssetValidation).filter(AssetValidation.asset_id == asset_id).delete()
            
            # Delete the asset
            self.db.delete(asset)
            self.db.commit()
            
            self.logger.warning(f"Hard deleted asset: {asset.name} (ID: {asset.id})")
            return True
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Failed to hard delete asset {asset_id}: {e}")
            raise
    
    # ==================== LOCKING MECHANISMS ====================
    
    def acquire_lock(self, asset_id: uuid.UUID, 
                    lock_type: str = "write",
                    duration_minutes: int = 30,
                    operation: str = None) -> str:
        """Acquire a lock on an asset."""
        try:
            # Check for existing locks
            existing_lock = self.db.query(AssetLock).filter(
                AssetLock.asset_id == asset_id,
                AssetLock.is_active == True,
                AssetLock.expires_at > datetime.utcnow()
            ).first()
            
            if existing_lock:
                if existing_lock.locked_by == self.user_id:
                    # Extend existing lock
                    existing_lock.expires_at = datetime.utcnow() + timedelta(minutes=duration_minutes)
                    self.db.commit()
                    return str(existing_lock.id)
                else:
                    raise AssetLockError(f"Asset {asset_id} is already locked by {existing_lock.locked_by}")
            
            # Create new lock
            lock = AssetLock(
                asset_id=asset_id,
                lock_type=lock_type,
                locked_by=self.user_id,
                expires_at=datetime.utcnow() + timedelta(minutes=duration_minutes),
                session_id=self.session_id,
                operation=operation
            )
            
            self.db.add(lock)
            self.db.commit()
            
            self.logger.info(f"Acquired {lock_type} lock on asset {asset_id}")
            return str(lock.id)
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Failed to acquire lock on asset {asset_id}: {e}")
            raise
    
    def release_lock(self, lock_id: str) -> bool:
        """Release an asset lock."""
        try:
            lock = self.db.query(AssetLock).filter(AssetLock.id == lock_id).first()
            if not lock:
                return False
            
            if lock.locked_by != self.user_id:
                raise AssetLockError(f"Lock {lock_id} is owned by {lock.locked_by}")
            
            lock.is_active = False
            lock.released_at = datetime.utcnow()
            lock.released_by = self.user_id
            
            self.db.commit()
            self.logger.info(f"Released lock {lock_id}")
            return True
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Failed to release lock {lock_id}: {e}")
            raise
    
    def _is_asset_locked(self, asset_id: uuid.UUID, lock_type: str = "write") -> bool:
        """Check if asset is locked."""
        lock = self.db.query(AssetLock).filter(
            AssetLock.asset_id == asset_id,
            AssetLock.lock_type.in_([lock_type, "exclusive"]),
            AssetLock.is_active == True,
            AssetLock.expires_at > datetime.utcnow(),
            AssetLock.locked_by != self.user_id  # Don't block own locks
        ).first()
        
        return lock is not None
    
    # ==================== HELPER METHODS ====================
    
    def _create_audit_log(self, asset_id: uuid.UUID, action: AuditAction, 
                         table_name: str, record_id: str, **kwargs) -> AssetAuditLog:
        """Create audit log entry."""
        audit_log = AssetAuditLog(
            asset_id=asset_id,
            action=action,
            table_name=table_name,
            record_id=record_id,
            user_id=self.user_id,
            session_id=self.session_id,
            **kwargs
        )
        
        self.db.add(audit_log)
        return audit_log
    
    def _create_version(self, asset: RobustAsset, change_reason: str, change_type: str) -> AssetVersion:
        """Create asset version snapshot."""
        version = AssetVersion(
            asset_id=asset.id,
            version_number=asset.version,
            created_by=self.user_id,
            change_reason=change_reason,
            change_type=change_type,
            asset_data=self._create_asset_snapshot(asset),
            checksum=asset.checksum,
            is_major_version=asset.version % 10 == 0  # Every 10th version is major
        )
        
        self.db.add(version)
        return version
    
    def _create_asset_snapshot(self, asset: RobustAsset) -> Dict[str, Any]:
        """Create complete asset data snapshot."""
        return self._asset_to_dict(asset)
    
    def _asset_to_dict(self, asset: RobustAsset) -> Dict[str, Any]:
        """Convert asset to dictionary."""
        return {
            "id": str(asset.id),
            "name": asset.name,
            "asset_type": asset.asset_type,
            "provider": asset.provider,
            "status": asset.status,
            "provider_id": asset.provider_id,
            "provider_region": asset.provider_region,
            "ip_addresses": asset.ip_addresses,
            "dns_names": asset.dns_names,
            "environment": asset.environment,
            "owner": asset.owner,
            "team": asset.team,
            "risk_score": asset.risk_score,
            "risk_level": asset.risk_level,
            "configuration": asset.configuration,
            "properties": asset.properties,
            "tags": asset.tags,
            "health_status": asset.health_status,
            "data_classification": asset.data_classification,
            "business_criticality": asset.business_criticality,
        }
    
    def _calculate_checksum(self, data: Dict[str, Any]) -> str:
        """Calculate SHA256 checksum of asset data."""
        # Sort keys for consistent hashing
        sorted_data = json.dumps(data, sort_keys=True, default=str)
        return hashlib.sha256(sorted_data.encode()).hexdigest()
    
    def _validate_asset(self, asset: RobustAsset) -> List[str]:
        """Validate asset data and return list of errors."""
        errors = []
        
        # Basic validation
        if not asset.name or len(asset.name.strip()) == 0:
            errors.append("Asset name is required")
        
        if not asset.asset_type:
            errors.append("Asset type is required")
        
        if not asset.provider:
            errors.append("Asset provider is required")
        
        # Business rule validation
        if asset.risk_score is not None and (asset.risk_score < 0 or asset.risk_score > 100):
            errors.append("Risk score must be between 0 and 100")
        
        return errors
    
    def _is_significant_change(self, old_values: Dict[str, Any], new_values: Dict[str, Any]) -> bool:
        """Determine if changes are significant enough to create a version."""
        significant_fields = {
            'asset_type', 'provider', 'status', 'risk_level', 
            'configuration', 'ip_addresses', 'dns_names'
        }
        
        for field in significant_fields:
            if field in new_values and old_values.get(field) != new_values[field]:
                return True
        
        return False
