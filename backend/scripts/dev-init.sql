-- Development database initialization script
-- This script sets up the development database with sample data

-- Create development database if it doesn't exist
SELECT 'CREATE DATABASE blast_radius_dev'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'blast_radius_dev');

-- Connect to the development database
\c blast_radius_dev;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Development-specific configurations
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_duration = on;
ALTER SYSTEM SET log_min_duration_statement = 0;

-- Reload configuration
SELECT pg_reload_conf();

-- Create development user with appropriate permissions
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_user WHERE usename = 'blast_radius_dev') THEN
        CREATE USER blast_radius_dev WITH PASSWORD 'dev_password_123';
    END IF;
END
$$;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE blast_radius_dev TO blast_radius_dev;
GRANT ALL ON SCHEMA public TO blast_radius_dev;

-- Development-specific settings
COMMENT ON DATABASE blast_radius_dev IS 'Blast-Radius Security Tool - Development Database';

-- Log successful initialization
INSERT INTO pg_stat_statements_info (dealloc) VALUES (0) ON CONFLICT DO NOTHING;
