Blast-Radius Security Tool Documentation
==========================================

Welcome to the comprehensive documentation for the Blast-Radius Security Tool, a cutting-edge security platform designed for purple teams, SOC operators, security architects, and red teamers.

.. image:: _static/logo.png
   :alt: Blast-Radius Security Tool Logo
   :align: center
   :width: 300px

Overview
--------

The Blast-Radius Security Tool provides real-time attack path analysis, multi-cloud integration, and deep ServiceNow CMDB integration to enable proactive security posture management and incident response.

Key Features
~~~~~~~~~~~~

* **Real-time Attack Path Analysis**: 5-degree attack path mapping with advanced graph algorithms
* **Multi-Cloud Integration**: Native support for AWS, Azure, and GCP environments
* **Role-Based Dashboards**: Customized interfaces for different security roles
* **Threat Intelligence**: STIX/TAXII integration with automated IOC correlation
* **Automated Remediation**: Configurable response workflows with approval processes
* **ServiceNow Integration**: Bi-directional CMDB synchronization and incident management

Target Users
~~~~~~~~~~~~

* :user-role:`SOC Operators` - Real-time monitoring and incident response
* :user-role:`Security Architects` - Risk assessment and security design
* :user-role:`Red Team Members` - Attack simulation and path discovery
* :user-role:`Purple Team Members` - Collaborative security testing and validation

Quick Start
-----------

.. toctree::
   :maxdepth: 2
   :caption: Getting Started

   installation
   configuration
   quick-start-guide

User Guides
-----------

Role-specific documentation for different user types:

.. toctree::
   :maxdepth: 2
   :caption: User Guides

   user-guides/soc-operators
   user-guides/security-architects
   user-guides/red-team-members
   user-guides/purple-team-members
   user-guides/administrators

API Documentation
-----------------

Comprehensive API reference and integration guides:

.. toctree::
   :maxdepth: 2
   :caption: API Reference

   api/authentication
   api/user-management
   api/asset-management
   api/attack-path-analysis
   api/threat-intelligence
   api/monitoring
   api/integrations

Technical Documentation
-----------------------

In-depth technical information for developers and system administrators:

.. toctree::
   :maxdepth: 2
   :caption: Technical Docs

   technical/architecture
   technical/database-design
   technical/graph-analysis
   technical/security-model
   technical/performance-tuning
   technical/deployment
   technical/monitoring

Development
-----------

Information for contributors and developers:

.. toctree::
   :maxdepth: 2
   :caption: Development

   development/setup
   development/workflow
   development/testing
   development/contributing
   development/code-standards

Features by Use Case
--------------------

Detailed documentation for each major use case:

.. toctree::
   :maxdepth: 2
   :caption: Use Cases

   use-cases/authentication-acl
   use-cases/asset-discovery
   use-cases/attack-path-analysis
   use-cases/monitoring-dashboard
   use-cases/threat-intelligence
   use-cases/automated-remediation
   use-cases/multi-cloud-integration
   use-cases/servicenow-integration

Security & Compliance
---------------------

Security considerations and compliance information:

.. toctree::
   :maxdepth: 2
   :caption: Security

   security/access-control
   security/data-protection
   security/audit-logging
   security/compliance
   security/best-practices

Troubleshooting
---------------

Common issues and solutions:

.. toctree::
   :maxdepth: 2
   :caption: Troubleshooting

   troubleshooting/common-issues
   troubleshooting/performance
   troubleshooting/integration-issues
   troubleshooting/faq

Release Notes
-------------

.. toctree::
   :maxdepth: 1
   :caption: Releases

   releases/changelog
   releases/migration-guides
   releases/roadmap

Support
-------

* **GitHub Issues**: `Report bugs and request features <https://github.com/forkrul/blast-radius/issues>`_
* **Documentation**: This comprehensive guide
* **Community**: Join our community discussions

License
-------

This project is licensed under the MIT License. See the `LICENSE <https://github.com/forkrul/blast-radius/blob/master/LICENSE>`_ file for details.

Indices and Tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`

.. note::
   This documentation is continuously updated. For the latest information, 
   please refer to the `GitHub repository <https://github.com/forkrul/blast-radius>`_.

.. warning::
   This tool is designed for authorized security testing and monitoring only. 
   Ensure you have proper authorization before using it in any environment.
